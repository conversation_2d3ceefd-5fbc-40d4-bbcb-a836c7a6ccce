import {createRouter, createWebHistory} from 'vue-router';
const routes = [
    {
        path: '/',
        name: 'TheLogin',
        component: () => import('@/views/login/TheLogin.vue'),
    },
    {
        path: '/login',
        name: 'TheLogin',
        component: () => import('@/views/login/TheLogin.vue'),
    },
    {
        path: '/register',
        name: 'TheRegister',
        component: () => import('@/views/login/TheRegister.vue'),
    },
    // 前台首页
    {
        path: '/frontIndex',
        name: 'frontIndex',
        component: () => import('@/views/front/Index.vue'),
    },
    // 公告
    {
        path: '/announcements',
        name: 'Announcements',
        component: () => import('@/views/front/Announcements.vue'),
    },
    // 个人中心
    {
        path: '/personal',
        name: 'Personal',
        component: () => import('@/views/front/Personal.vue'),
    },
    // 维修
    {
        path: '/repair',
        name: 'Repair',
        component: () => import('@/views/front/Repair.vue'),
    },
    // 投诉
    {
        path: '/complaint',
        name: 'Complaint',
        component: () => import('@/views/front/Complaint.vue'),
    },
    // 访客
    {
        path: '/visitor',
        name: 'Visitor',
        component: () => import('@/views/front/Visitor.vue'),
    },
    // 账单
    {
        path: '/bills',
        name: 'Bills',
        component: () => import('@/views/front/Bills.vue'),
    },
    // 后台首页
    {
        path: '/backIndex',
        name: 'backIndex',
        component: () => import('@/views/back/Index.vue'),
    },
    // 住户管理
    {
        path: '/residents',
        name: 'Residents',
        component: () => import('@/views/back/residents.vue'),
    },
    // 工单管理
    {
        path: '/workorders',
        name: 'Workorders',
        component: () => import('@/views/back/repairOrder.vue'),
    },
    // 房产管理
    {
        path: '/houses',
        name: 'Houses',
        component: () => import('@/views/back/houses.vue'),
    },
    // 费用管理
    {
        path: '/finance',
        name: 'Finance',
        component: () => import('@/views/back/finance.vue'),
    },
    // 公告管理（后台）
    {
        path: '/announcements-manage',
        name: 'AnnouncementsManage',
        component: () => import('@/views/back/announcements.vue'),
    },
    // 访客管理
    {
        path: '/visitor-manage',
        name: 'VisitorManage',
        component: () => import('@/views/back/visitor.vue'),
    },
    // 个人中心
    {
        path: '/profile',
        name: 'Profile',
        component: () => import('@/views/back/profile.vue'),
    },
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import('@/views/404.vue')
    },
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

export default router;