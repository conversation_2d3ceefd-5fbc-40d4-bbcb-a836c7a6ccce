# 公告管理系统演示指南

## 功能演示步骤

### 1. 启动项目
```bash
cd pms-front-master
npm run dev
```
访问: http://localhost:5175

### 2. 导航到公告管理
- 登录后台管理系统
- 点击左侧菜单中的"公告通知"

### 3. 查看公告列表
- 页面会自动加载公告列表
- 顶部显示统计卡片：公告总数、已发布、草稿、总阅读量
- 公告以卡片形式展示，包含类型标签、标题、内容预览等

### 4. 搜索功能演示
- 在搜索框中输入关键词（如"消防"、"春节"等）
- 按回车键或点击"搜索"按钮
- 列表会根据搜索条件过滤显示

### 5. 筛选功能演示
- 点击"全部类型"下拉菜单
- 选择特定类型（通知、紧急通知、活动、公告）
- 列表会按选择的类型进行筛选

### 6. 添加公告演示
- 点击右上角"发布公告"按钮
- 填写表单：
  - 公告标题：输入有意义的标题
  - 公告类型：选择合适的类型
  - 发布人：从下拉列表选择
  - 发布状态：选择"已发布"或"草稿"
  - 公告内容：输入详细内容
- 点击"确定"提交

### 7. 编辑公告演示
- 在公告卡片上点击"编辑"按钮
- 表单会预填充现有数据
- 修改需要更新的字段
- 点击"确定"保存更改

### 8. 查看详情演示
- 在公告卡片上点击"查看"按钮
- 弹出详情对话框，显示完整信息：
  - 公告标题和状态
  - 类型、发布人、创建时间、阅读量
  - 完整的公告内容

### 9. 删除公告演示
- 在公告卡片上点击"删除"按钮
- 系统会弹出确认对话框
- 确认后公告将被删除，列表自动刷新

### 10. 分页功能演示
- 如果公告数量较多，底部会显示分页组件
- 可以切换页码、调整每页显示数量
- 支持跳转到指定页面

## 界面特色

### 1. 响应式设计
- 桌面端：完整的多列布局
- 移动端：自适应单列布局
- 对话框在小屏幕上自动调整大小

### 2. 用户体验优化
- 加载状态：所有异步操作都有loading指示器
- 错误处理：友好的错误提示信息
- 成功反馈：操作成功后的确认消息
- 防误操作：删除前的二次确认

### 3. 视觉设计
- 统一的色彩方案
- 清晰的信息层次
- 直观的操作按钮
- 美观的统计卡片

## API接口状态

所有功能都已接入真实的API接口：

✅ **查询接口**: `getAnnouncementListApi` - 支持分页、搜索、筛选
✅ **添加接口**: `addAnnouncementApi` - 创建新公告
✅ **编辑接口**: `editAnnouncementApi` - 更新现有公告
✅ **删除接口**: `deleteAnnouncementApi` - 删除公告
✅ **详情接口**: `getAnnouncementByIdApi` - 获取公告详情
✅ **用户接口**: `getAllUserListApi` - 获取发布人列表

## 数据验证

表单包含完整的前端验证：
- 标题：必填，2-100字符
- 内容：必填，10-2000字符
- 类型：必选
- 发布人：必选

## 测试建议

1. **功能测试**：
   - 测试所有CRUD操作
   - 验证搜索和筛选功能
   - 检查分页功能

2. **边界测试**：
   - 输入超长文本
   - 输入特殊字符
   - 网络异常情况

3. **用户体验测试**：
   - 在不同设备上测试响应式布局
   - 测试加载状态和错误处理
   - 验证操作流程的流畅性

## 注意事项

1. **数据格式**：确保后端API返回的数据格式与前端期望一致
2. **权限控制**：根据用户角色控制操作权限
3. **性能优化**：大量数据时考虑虚拟滚动或更高效的分页
4. **安全性**：对用户输入进行适当的验证和过滤

## 后续扩展

1. **富文本编辑器**：为公告内容添加格式化功能
2. **文件上传**：支持在公告中插入图片或附件
3. **批量操作**：支持批量删除、批量发布等
4. **通知推送**：公告发布后自动推送给相关用户
5. **审核流程**：添加公告审核机制
6. **模板功能**：预设常用公告模板
